import tensorflow as tf
import numpy as np

@tf.custom_gradient
def QuantizationOp(x, B):
    """
    将 x ∈ [0,1) 量化成 B bit 序列
    
    Args:
        x: [batch, N] float32, 输入特征
        B: int, 量化比特数
    
    Returns:
        bits: [batch, N*B] float32, 量化后的比特序列 (0/1)
    """
    # 前向传播
    B = tf.cast(B, tf.int32)
    step = tf.pow(2.0, tf.cast(B, tf.float32))  # 2^B
    
    batch = tf.shape(x)[0]
    N = tf.shape(x)[1]
    
    # 将输入限制在 [0, 1-1/step) 范围内，防止溢出
    x_clip = tf.clip_by_value(x, 0.0, 1.0 - 1.0/step)
    
    # 均匀量化
    y_int = tf.cast(tf.floor(x_clip * step), tf.int32)  # [batch, N]
    
    # 位展开：将整数转换为二进制表示
    y_exp = tf.expand_dims(y_int, -1)  # [batch, N, 1]
    
    # 创建位掩码 [1, 2, 4, 8, ...]
    masks = tf.bitwise.left_shift(
        tf.ones((1,), tf.int32), 
        tf.range(B, dtype=tf.int32)
    )  # [B]
    
    # 按位与操作，提取每一位
    bits_lsb = tf.bitwise.bitwise_and(y_exp, masks)  # [batch, N, B]
    bits_lsb = tf.cast(bits_lsb > 0, tf.float32)  # 转换为0/1
    
    # 翻转为MSB→LSB顺序并重塑
    bits = tf.reverse(bits_lsb, axis=[-1])  # [batch, N, B]
    bits = tf.reshape(bits, (batch, N * B))  # [batch, N*B]
    
    # 自定义梯度 (直通估计器)
    def grad(dy):
        # 对于量化操作，使用直通估计器
        # 梯度直接传递，不对量化比特数求梯度
        return dy, None
    
    return bits, grad

@tf.custom_gradient  
def DequantizationOp(bits, B, original_shape):
    """
    将比特序列反量化为连续值
    
    Args:
        bits: [batch, N*B] float32, 比特序列
        B: int, 量化比特数
        original_shape: tuple, 原始特征的形状 [batch, N]
    
    Returns:
        x_reconstructed: [batch, N] float32, 重建的连续值
    """
    # 前向传播
    B = tf.cast(B, tf.int32)
    step = tf.pow(2.0, tf.cast(B, tf.float32))
    
    batch = original_shape[0]
    N = original_shape[1]
    
    # 重塑比特序列
    bits_reshaped = tf.reshape(bits, [batch, N, B])  # [batch, N, B]
    
    # 翻转回LSB→MSB顺序
    bits_lsb = tf.reverse(bits_reshaped, axis=[-1])  # [batch, N, B]
    
    # 创建权重 [1, 2, 4, 8, ...]
    weights = tf.pow(2.0, tf.cast(tf.range(B, dtype=tf.float32), tf.float32))  # [B]
    
    # 计算整数值
    y_int = tf.reduce_sum(bits_lsb * weights[tf.newaxis, tf.newaxis, :], axis=-1)  # [batch, N]
    
    # 反量化到 [0, 1) 范围
    x_reconstructed = y_int / step
    
    # 自定义梯度
    def grad(dy):
        return dy, None, None
    
    return x_reconstructed, grad

class QuantizationLayer(tf.keras.layers.Layer):
    """量化层的封装"""

    def __init__(self, quantization_bits, **kwargs):
        super(QuantizationLayer, self).__init__(**kwargs)
        self.quantization_bits = quantization_bits

    def call(self, inputs, training=None):
        """
        Args:
            inputs: [batch, features] float32, 输入特征

        Returns:
            quantized: [batch, features * quantization_bits] float32, 量化后的比特
        """
        # 简化：训练和推理都使用相同的量化操作
        return QuantizationOp(inputs, self.quantization_bits)
    
    def get_config(self):
        config = super().get_config()
        config.update({
            'quantization_bits': self.quantization_bits
        })
        return config

class DequantizationLayer(tf.keras.layers.Layer):
    """反量化层的封装 - 简化实现避免梯度问题"""

    def __init__(self, quantization_bits, original_features, **kwargs):
        super(DequantizationLayer, self).__init__(**kwargs)
        self.quantization_bits = quantization_bits
        self.original_features = original_features

    def call(self, inputs, training=None):
        """
        Args:
            inputs: [batch, features * quantization_bits] float32, 量化的比特序列

        Returns:
            dequantized: [batch, original_features] float32, 反量化后的特征
        """
        # 简化实现：直接使用线性变换近似反量化
        # 这避免了复杂的自定义梯度问题
        batch_size = tf.shape(inputs)[0]

        # 重塑输入
        bits_reshaped = tf.reshape(inputs, [batch_size, self.original_features, self.quantization_bits])

        # 使用权重进行反量化
        weights = tf.pow(2.0, tf.cast(tf.range(self.quantization_bits, dtype=tf.float32), tf.float32))
        weights = tf.reverse(weights, axis=[0])  # MSB到LSB

        # 计算反量化值
        dequantized = tf.reduce_sum(bits_reshaped * weights[tf.newaxis, tf.newaxis, :], axis=-1)
        dequantized = dequantized / tf.pow(2.0, tf.cast(self.quantization_bits, tf.float32))

        return dequantized
    
    def get_config(self):
        config = super().get_config()
        config.update({
            'quantization_bits': self.quantization_bits,
            'original_features': self.original_features
        })
        return config

# 测试函数
def test_quantization():
    """测试量化和反量化操作"""
    # 创建测试数据
    batch_size = 4
    features = 8
    quantization_bits = 4
    
    # 随机输入 [0, 1)
    x = tf.random.uniform([batch_size, features], 0.0, 1.0)
    print(f"Original input shape: {x.shape}")
    print(f"Original input range: [{tf.reduce_min(x):.3f}, {tf.reduce_max(x):.3f}]")
    
    # 量化
    quantized = QuantizationOp(x, quantization_bits)
    print(f"Quantized shape: {quantized.shape}")
    print(f"Quantized values (first sample): {quantized[0, :quantization_bits*2]}")
    
    # 反量化
    original_shape = tf.shape(x)
    dequantized = DequantizationOp(quantized, quantization_bits, original_shape)
    print(f"Dequantized shape: {dequantized.shape}")
    print(f"Dequantized range: [{tf.reduce_min(dequantized):.3f}, {tf.reduce_max(dequantized):.3f}]")
    
    # 计算重建误差
    mse = tf.reduce_mean(tf.square(x - dequantized))
    print(f"Reconstruction MSE: {mse:.6f}")

if __name__ == "__main__":
    test_quantization()
