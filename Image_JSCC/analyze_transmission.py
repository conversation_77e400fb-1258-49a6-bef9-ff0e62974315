#!/usr/bin/env python3
"""
分析Image JSCC的传输信息量
"""

import numpy as np
from config import ImageJSCCConfig

def analyze_transmission_capacity():
    """分析传输容量"""
    config = ImageJSCCConfig()
    
    print("🔍 Image JSCC 传输容量分析")
    print("=" * 50)
    
    # 原始图像信息量
    original_pixels = config.image_height * config.image_width * config.image_channels
    original_bits = original_pixels * 8  # 假设每个像素8位
    
    print(f"📊 原始图像信息:")
    print(f"  - 图像尺寸: {config.image_height}×{config.image_width}×{config.image_channels}")
    print(f"  - 总像素数: {original_pixels:,}")
    print(f"  - 原始比特数: {original_bits:,} bits")
    print(f"  - 原始大小: {original_bits/8/1024:.1f} KB")
    
    # 编码后特征维度
    # 假设编码器将图像压缩到 (H/8) × (W/8) 的特征图
    feature_h = config.image_height // 8
    feature_w = config.image_width // 8
    compressed_features = (feature_h * feature_w) // config.compression_ratio
    
    print(f"\n🔧 编码器输出:")
    print(f"  - 特征图尺寸: {feature_h}×{feature_w}")
    print(f"  - 压缩比: {config.compression_ratio}")
    print(f"  - 压缩后特征数: {compressed_features}")
    
    # 量化后比特数
    quantized_bits = compressed_features * config.quantization_bits
    
    print(f"\n⚡ 量化信息:")
    print(f"  - 量化比特数: {config.quantization_bits}")
    print(f"  - 量化后总比特数: {quantized_bits}")
    
    # 实际传输比特数
    transmitted_bits = config.feedback_bits
    
    print(f"\n📡 传输信息:")
    print(f"  - 配置的传输比特数: {transmitted_bits}")
    print(f"  - 量化产生的比特数: {quantized_bits}")
    
    if transmitted_bits < quantized_bits:
        print(f"  ⚠️  传输瓶颈: 传输比特数 < 量化比特数")
        print(f"     信息损失: {quantized_bits - transmitted_bits} bits")
    else:
        print(f"  ✅ 传输容量充足")
    
    # 压缩比分析
    compression_ratio_actual = original_bits / transmitted_bits
    
    print(f"\n📈 压缩比分析:")
    print(f"  - 实际压缩比: {compression_ratio_actual:.1f}:1")
    print(f"  - 传输效率: {transmitted_bits/original_bits*100:.2f}%")
    
    # 理论PSNR估算
    # 基于信息论，压缩比越高，重建质量越低
    if compression_ratio_actual > 100:
        estimated_psnr = 15 + 5 * np.log10(transmitted_bits / 1000)
    else:
        estimated_psnr = 20 + 10 * np.log10(transmitted_bits / 2000)
    
    print(f"\n🎯 质量预估:")
    print(f"  - 理论PSNR范围: {estimated_psnr:.1f} ± 5 dB")
    
    # 建议
    print(f"\n💡 优化建议:")
    
    if transmitted_bits < quantized_bits:
        print("1. 🚨 增加传输比特数 (feedback_bits)")
        suggested_bits = int(quantized_bits * 1.2)
        print(f"   建议: feedback_bits = {suggested_bits}")
    
    if compression_ratio_actual > 200:
        print("2. 🔧 降低压缩比")
        print(f"   当前压缩比过高 ({compression_ratio_actual:.1f}:1)")
        print(f"   建议: compression_ratio = 1 (不压缩)")
    
    if config.quantization_bits < 6:
        print("3. 📊 增加量化比特数")
        print(f"   当前: {config.quantization_bits} bits")
        print(f"   建议: 6-8 bits")
    
    return {
        'original_bits': original_bits,
        'compressed_features': compressed_features,
        'quantized_bits': quantized_bits,
        'transmitted_bits': transmitted_bits,
        'compression_ratio': compression_ratio_actual,
        'estimated_psnr': estimated_psnr
    }

def suggest_optimal_config():
    """建议最优配置"""
    config = ImageJSCCConfig()
    
    print("\n🎯 最优配置建议:")
    print("=" * 30)
    
    # 计算合理的传输比特数
    original_pixels = config.image_height * config.image_width * config.image_channels
    
    # 目标: PSNR > 25dB, 需要合理的信息传输量
    # 经验值: 每个像素需要传输 1-2 bits 来保证较好质量
    target_bits_per_pixel = 1.5
    suggested_feedback_bits = int(original_pixels * target_bits_per_pixel)
    
    print(f"建议配置:")
    print(f"  compression_ratio = 1      # 不压缩，保留所有特征")
    print(f"  quantization_bits = 8      # 高精度量化")
    print(f"  feedback_bits = {suggested_feedback_bits}    # 充足的传输容量")
    
    # 计算这种配置下的理论性能
    feature_h = config.image_height // 8
    feature_w = config.image_width // 8
    features_no_compression = feature_h * feature_w
    quantized_bits_optimal = features_no_compression * 8
    
    print(f"\n预期效果:")
    print(f"  - 特征数: {features_no_compression}")
    print(f"  - 量化比特数: {quantized_bits_optimal}")
    print(f"  - 传输比特数: {suggested_feedback_bits}")
    print(f"  - 预期PSNR: 25-35 dB")

def main():
    """主函数"""
    print("🚀 开始传输容量分析...")
    
    # 分析当前配置
    results = analyze_transmission_capacity()
    
    # 建议最优配置
    suggest_optimal_config()
    
    print(f"\n📋 总结:")
    if results['transmitted_bits'] < results['quantized_bits']:
        print("❌ 当前配置存在传输瓶颈")
        print("   传输比特数不足以承载量化后的信息")
        print("   这很可能是PSNR低的主要原因!")
    else:
        print("✅ 传输容量基本充足")
        print("   PSNR低可能由其他因素造成")

if __name__ == "__main__":
    main()
