#!/usr/bin/env python3
"""
测试去掉量化后的模型
"""

import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
import os

# 设置GPU
os.environ['CUDA_VISIBLE_DEVICES'] = '1'
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
    except RuntimeError as e:
        print(e)

from config import ImageJSCCConfig
from model.image_jscc_model import create_model
from data.image_dataset import get_image_dataset
from loss.image_loss import create_loss_function, ImageMetrics

def test_model_without_quantization():
    """测试去掉量化的模型"""
    print("🧪 测试去掉量化的Image JSCC模型...")
    
    config = ImageJSCCConfig()
    print(f"配置: {config.image_height}×{config.image_width}, batch={config.batch_size}")
    
    # 创建模型
    print("创建模型...")
    model = create_model(config)
    
    # 获取测试数据
    print("加载测试数据...")
    dataset = get_image_dataset(config, 1, dataset_type='train')
    
    for x_batch in dataset.take(1):
        print(f"输入图像: {x_batch.shape}, 范围: [{tf.reduce_min(x_batch):.4f}, {tf.reduce_max(x_batch):.4f}]")
        
        # 逐步测试各个组件
        print("\n🔍 逐步测试各组件:")
        
        # 1. 编码器
        encoded = model.encoder(x_batch, training=False)
        print(f"1. 编码器输出: {encoded.shape}, 范围: [{tf.reduce_min(encoded):.4f}, {tf.reduce_max(encoded):.4f}]")
        
        # 2. 信道 (直接传输float)
        received = model.channel(encoded, snr_db=config.snr_dB, training=False)
        print(f"2. 信道输出: {received.shape}, 范围: [{tf.reduce_min(received):.4f}, {tf.reduce_max(received):.4f}]")
        
        # 3. 解码器
        reconstructed = model.decoder(received, training=False)
        print(f"3. 解码器输出: {reconstructed.shape}, 范围: [{tf.reduce_min(reconstructed):.4f}, {tf.reduce_max(reconstructed):.4f}]")
        
        # 4. 完整模型
        full_output = model(x_batch, training=False)
        print(f"4. 完整模型输出: {full_output.shape}, 范围: [{tf.reduce_min(full_output):.4f}, {tf.reduce_max(full_output):.4f}]")
        
        # 计算指标
        metrics_fn = ImageMetrics()
        metrics = metrics_fn(x_batch, full_output)
        
        print(f"\n📊 质量指标:")
        print(f"  PSNR: {metrics['psnr']:.2f} dB")
        print(f"  SSIM: {metrics['ssim']:.4f}")
        print(f"  MSE: {metrics['mse']:.6f}")
        
        # 可视化对比
        visualize_results(x_batch, full_output, metrics)
        
        return x_batch, full_output, metrics

def visualize_results(original, reconstructed, metrics):
    """可视化结果"""
    print("\n📊 生成对比图...")
    
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # 原图
    original_img = np.clip(original[0].numpy(), 0, 1)
    axes[0].imshow(original_img)
    axes[0].set_title('Original Image')
    axes[0].axis('off')
    
    # 重建图
    reconstructed_img = np.clip(reconstructed[0].numpy(), 0, 1)
    axes[1].imshow(reconstructed_img)
    axes[1].set_title(f'Reconstructed (No Quantization)\nPSNR: {metrics["psnr"]:.2f}dB')
    axes[1].axis('off')
    
    # 差异图
    diff = np.abs(original_img - reconstructed_img)
    im = axes[2].imshow(diff, cmap='hot')
    axes[2].set_title('Absolute Difference')
    axes[2].axis('off')
    plt.colorbar(im, ax=axes[2])
    
    plt.tight_layout()
    plt.savefig('test_no_quantization_result.png', dpi=150, bbox_inches='tight')
    plt.show()
    print("结果已保存为 'test_no_quantization_result.png'")

def test_training_step():
    """测试训练步骤"""
    print("\n🏋️ 测试训练步骤...")
    
    config = ImageJSCCConfig()
    model = create_model(config)
    optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)
    loss_fn = create_loss_function('mse')
    
    # 获取测试数据
    dataset = get_image_dataset(config, 2, dataset_type='train')
    
    for x_batch in dataset.take(1):
        print(f"训练数据: {x_batch.shape}")
        
        # 训练前
        y_pred_before = model(x_batch, training=False)
        loss_before = loss_fn(x_batch, y_pred_before)
        print(f"训练前 - 损失: {loss_before:.6f}, 输出范围: [{tf.reduce_min(y_pred_before):.4f}, {tf.reduce_max(y_pred_before):.4f}]")
        
        # 执行几个训练步骤
        for step in range(5):
            with tf.GradientTape() as tape:
                y_pred = model(x_batch, training=True)
                loss = loss_fn(x_batch, y_pred)
            
            gradients = tape.gradient(loss, model.trainable_variables)
            optimizer.apply_gradients(zip(gradients, model.trainable_variables))
            
            print(f"  步骤 {step+1}: 损失={loss:.6f}, 输出范围=[{tf.reduce_min(y_pred):.4f}, {tf.reduce_max(y_pred):.4f}]")
        
        # 训练后
        y_pred_after = model(x_batch, training=False)
        loss_after = loss_fn(x_batch, y_pred_after)
        print(f"训练后 - 损失: {loss_after:.6f}, 输出范围: [{tf.reduce_min(y_pred_after):.4f}, {tf.reduce_max(y_pred_after):.4f}]")
        
        # 检查改善
        improvement = (loss_before - loss_after) / loss_before * 100
        print(f"损失改善: {improvement:.2f}%")
        
        break

def main():
    """主函数"""
    print("🚀 开始测试去掉量化的模型...")
    
    try:
        # 1. 基本功能测试
        original, reconstructed, metrics = test_model_without_quantization()
        
        # 2. 训练步骤测试
        test_training_step()
        
        print("\n💡 结论:")
        if metrics['psnr'] > 15:
            print("✅ 去掉量化后模型工作正常")
            print("   问题可能确实在量化部分")
            print("   建议：")
            print("   1. 先用float训练模型到收敛")
            print("   2. 再逐步添加量化功能")
            print("   3. 检查量化/反量化的实现")
        else:
            print("❌ 去掉量化后问题依然存在")
            print("   问题可能在：")
            print("   1. 编码器/解码器结构")
            print("   2. 信道实现")
            print("   3. 损失函数")
            print("   4. 数据预处理")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
