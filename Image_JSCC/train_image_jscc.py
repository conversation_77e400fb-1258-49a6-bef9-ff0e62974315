# 系统配置
import os
if os.getenv("CUDA_VISIBLE_DEVICES") is None:
    gpu_num = 0  # 指定使用GPU ID
    os.environ["CUDA_VISIBLE_DEVICES"] = f"{gpu_num}"
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

# TensorFlow配置
import tensorflow as tf
gpus = tf.config.list_physical_devices('GPU')
if gpus:
    try:
        tf.config.experimental.set_memory_growth(gpus[0], True)
    except RuntimeError as e:
        print(e)
tf.get_logger().setLevel('ERROR')

# 导入必要的库
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
from datetime import datetime
import pickle
import sys
import wandb

# 导入自定义模块
from config import ImageJSCCConfig
from data.image_dataset import get_image_dataset
from model.image_jscc_model import create_model
from loss.image_loss import create_loss_function, ImageMetrics

def init_log(log_path):
    """初始化日志文件"""
    with open(log_path, 'w') as f:
        f.write("epoch,step,train_loss,val_loss,val_mse,val_psnr,val_ssim,val_mae\n")

def log_metrics(log_path, epoch, step, train_loss, val_loss=None, val_mse=None, val_psnr=None, val_ssim=None, val_mae=None):
    """记录训练指标"""
    with open(log_path, 'a') as f:
        f.write(f"{epoch},{step},{train_loss},{val_loss},{val_mse},{val_psnr},{val_ssim},{val_mae}\n")

@tf.function
def train_step(model, optimizer, loss_fn, x_batch, gradient_clip_norm=None):
    """单步训练"""
    with tf.GradientTape() as tape:
        y_pred = model(x_batch, training=True)
        loss = loss_fn(x_batch, y_pred)

    # 计算梯度
    gradients = tape.gradient(loss, model.trainable_variables)

    # 梯度裁剪
    if gradient_clip_norm is not None:
        gradients, _ = tf.clip_by_global_norm(gradients, gradient_clip_norm)

    # 更新参数
    optimizer.apply_gradients(zip(gradients, model.trainable_variables))

    return loss

def evaluate_model(model, val_dataset, loss_fn, metrics_fn, num_batches=None):
    """评估模型"""
    total_loss = 0.0
    total_metrics = {'mse': 0.0, 'psnr': 0.0, 'ssim': 0.0, 'mae': 0.0}
    num_samples = 0
    batch_count = 0
    
    for x_batch in val_dataset:
        if num_batches is not None and batch_count >= num_batches:
            break
            
        # 前向传播
        y_pred = model(x_batch, training=False)
        
        # 计算损失
        batch_loss = loss_fn(x_batch, y_pred)
        total_loss += batch_loss * tf.shape(x_batch)[0]
        
        # 计算指标
        batch_metrics = metrics_fn(x_batch, y_pred)
        for key, value in batch_metrics.items():
            total_metrics[key] += value * tf.shape(x_batch)[0]
        
        num_samples += tf.shape(x_batch)[0]
        batch_count += 1
    
    # 计算平均值
    avg_loss = total_loss / num_samples
    avg_metrics = {key: value / num_samples for key, value in total_metrics.items()}
    
    return avg_loss, avg_metrics

def save_sample_images(model, val_dataset, save_path, epoch, num_samples=4):
    """保存样本图像对比"""
    import matplotlib.pyplot as plt
    
    # 获取一个批次的数据
    for x_batch in val_dataset.take(1):
        y_pred = model(x_batch[:num_samples], training=False)
        
        # 创建对比图
        fig, axes = plt.subplots(2, num_samples, figsize=(num_samples*3, 6))
        
        for i in range(num_samples):
            # 原始图像
            axes[0, i].imshow(x_batch[i].numpy())
            axes[0, i].set_title(f'Original {i+1}')
            axes[0, i].axis('off')
            
            # 重建图像
            axes[1, i].imshow(y_pred[i].numpy())
            axes[1, i].set_title(f'Reconstructed {i+1}')
            axes[1, i].axis('off')
        
        plt.suptitle(f'Epoch {epoch} - Image Reconstruction Comparison')
        plt.tight_layout()
        plt.savefig(os.path.join(save_path, f'samples_epoch_{epoch}.png'), dpi=150, bbox_inches='tight')
        plt.close()
        break

def main():
    """主训练函数"""
    # 加载配置
    config = ImageJSCCConfig()

    # 初始化wandb
    wandb.init(
        project="image-jscc",
        name=f"jscc_snr{config.snr_dB}_comp{config.compression_ratio}_{config.currentDT}",
        config={
            "image_height": config.image_height,
            "image_width": config.image_width,
            "batch_size": config.batch_size,
            "epochs": config.epochs,
            "learning_rate": config.initial_lr,
            "compression_ratio": config.compression_ratio,
            "quantization_bits": config.quantization_bits,
            "snr_dB": config.snr_dB,
            "channel_type": config.channel_type,
        }
    )

    print("Configuration loaded:")
    print(f"  Image size: {config.image_height}x{config.image_width}x{config.image_channels}")
    print(f"  Batch size: {config.batch_size}")
    print(f"  Epochs: {config.epochs}")
    print(f"  Compression ratio: {config.compression_ratio}")
    print(f"  SNR: {config.snr_dB} dB")
    print(f"  Channel type: {config.channel_type}")

    # 创建数据集
    print("\nLoading datasets...")
    train_dataset = get_image_dataset(config, config.batch_size, dataset_type='train')
    val_dataset = get_image_dataset(config, config.batch_size, dataset_type='val')

    # 计算每个epoch的步数
    # 估算训练集大小 (所有城市约有2975张图片)
    estimated_train_size = 2975
    steps_per_epoch = max(1, estimated_train_size // config.batch_size)
    print(f"Steps per epoch: {steps_per_epoch}")

    # 创建模型
    print("\nCreating model...")
    model = create_model(config)
    model.summary_custom()

    # 创建学习率调度器
    if config.lr_decay_strategy == "CosineAnnealing":
        lr_schedule = tf.keras.optimizers.schedules.CosineDecay(
            initial_learning_rate=config.initial_lr,
            decay_steps=config.epochs * steps_per_epoch,
            alpha=config.min_lr / config.initial_lr
        )
    else:
        lr_schedule = tf.keras.optimizers.schedules.ExponentialDecay(
            initial_learning_rate=config.initial_lr,
            decay_steps=config.lr_decay_epochs * steps_per_epoch,
            decay_rate=config.exp_decay_rate,
            staircase=True
        )

    # 创建优化器
    optimizer = tf.keras.optimizers.Adam(learning_rate=lr_schedule)

    # 创建损失函数和指标 - 使用组合损失提升图像质量
    loss_fn = create_loss_function('combined',
                                   mse_weight=1.0,      # MSE权重
                                   ssim_weight=0.5,     # SSIM权重，提升感知质量
                                   perceptual_weight=0.0)  # 暂时不用感知损失，避免复杂度
    metrics_fn = ImageMetrics()

    # 创建保存目录
    os.makedirs(config.log_dir, exist_ok=True)
    os.makedirs(os.path.join(config.log_dir, 'samples'), exist_ok=True)

    # 初始化日志
    log_path = os.path.join(config.log_dir, 'training_log.csv')
    init_log(log_path)

    print(f"\nStarting training...")
    print(f"Logs will be saved to: {config.log_dir}")
    print(f"Checkpoints will be saved to: {config.ckpt_dir}")

    best_val_loss = float('inf')
    early_stopping_counter = 0

    # 训练循环
    for epoch in range(config.epochs):
        print(f'\nEpoch {epoch + 1}/{config.epochs}')

        # 训练阶段
        epoch_loss = 0.0
        train_dataset_epoch = get_image_dataset(config, config.batch_size, dataset_type='train')

        with tqdm(total=steps_per_epoch, desc=f"Training") as pbar:
            for step, x_batch in enumerate(train_dataset_epoch.take(steps_per_epoch)):
                # 训练步骤
                loss = train_step(model, optimizer, loss_fn, x_batch, config.gradient_clip_norm)
                epoch_loss += loss.numpy()

                # 记录到wandb (每20步记录一次，包含PSNR和SSIM)
                if step % 20 == 0:
                    # 计算当前batch的PSNR和SSIM
                    with tf.GradientTape():
                        y_pred = model(x_batch, training=False)

                    # 计算指标
                    psnr = tf.reduce_mean(tf.image.psnr(x_batch, y_pred, max_val=1.0))
                    ssim = tf.reduce_mean(tf.image.ssim(x_batch, y_pred, max_val=1.0))
                    mse = tf.reduce_mean(tf.square(x_batch - y_pred))

                    wandb.log({
                        "train_loss_step": loss.numpy(),
                        "train_psnr_step": psnr.numpy(),
                        "train_ssim_step": ssim.numpy(),
                        "train_mse_step": mse.numpy(),
                        "epoch": epoch + 1,
                        "step": step
                    })

                # 更新进度条 (每20步显示详细指标)
                if step % 20 == 0 and 'psnr' in locals():
                    pbar.set_postfix({
                        'loss': f'{loss.numpy():.4f}',
                        'psnr': f'{psnr.numpy():.2f}',
                        'ssim': f'{ssim.numpy():.3f}'
                    })
                else:
                    pbar.set_postfix({'loss': f'{loss.numpy():.4f}'})
                pbar.update(1)

        # 计算平均训练损失
        avg_train_loss = epoch_loss / steps_per_epoch

        # 验证阶段
        if (epoch + 1) % config.test_interval == 0 or (epoch + 1) == config.epochs:
            print("Evaluating...")
            val_loss, val_metrics = evaluate_model(
                model, val_dataset, loss_fn, metrics_fn, num_batches=10
            )

            print(f"Validation - Loss: {val_loss:.4f}, PSNR: {val_metrics['psnr']:.2f}, SSIM: {val_metrics['ssim']:.4f}")

            # 记录到wandb
            wandb.log({
                "epoch": epoch + 1,
                "train_loss_epoch": avg_train_loss,
                "val_loss": val_loss,
                "val_psnr": val_metrics['psnr'],
                "val_ssim": val_metrics['ssim'],
                "val_mse": val_metrics['mse'],
                "val_mae": val_metrics['mae'],
                "learning_rate": optimizer.learning_rate.numpy()
            })

            # 记录指标
            log_metrics(
                log_path, epoch + 1, steps_per_epoch, avg_train_loss,
                val_loss, val_metrics['mse'], val_metrics['psnr'],
                val_metrics['ssim'], val_metrics['mae']
            )

            # 保存最佳模型和早停检查
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                early_stopping_counter = 0  # 重置早停计数器
                best_model_path = os.path.join(config.ckpt_dir, 'best_model')
                model.save_weights_custom(best_model_path)
                print(f"Best model saved with validation loss: {val_loss:.4f}")
            else:
                early_stopping_counter += 1
                print(f"Validation loss did not improve. Early stopping counter: {early_stopping_counter}/{config.early_stopping_patience}")

                # 早停检查
                if early_stopping_counter >= config.early_stopping_patience:
                    print(f"Early stopping triggered after {epoch + 1} epochs")
                    break

            # 保存样本图像
            save_sample_images(
                model, val_dataset,
                os.path.join(config.log_dir, 'samples'),
                epoch + 1
            )
        else:
            # 只记录训练损失
            wandb.log({
                "epoch": epoch + 1,
                "train_loss_epoch": avg_train_loss
            })
            log_metrics(log_path, epoch + 1, steps_per_epoch, avg_train_loss)

    # 保存最终模型
    final_model_path = os.path.join(config.ckpt_dir, 'final_model')
    model.save_weights_custom(final_model_path)
    print(f"\nTraining completed! Final model saved to: {final_model_path}")
    print(f"Best validation loss: {best_val_loss:.4f}")

    # 关闭wandb
    wandb.finish()

if __name__ == "__main__":
    main()
