2025-08-05 11:00:04,899 INFO    MainThread:1027956 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-08-05 11:00:04,899 INFO    MainThread:1027956 [wandb_setup.py:_flush():80] Configure stats pid to 1027956
2025-08-05 11:00:04,899 INFO    MainThread:1027956 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-08-05 11:00:04,899 INFO    MainThread:1027956 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/GESCO/Image_JSCC/wandb/settings
2025-08-05 11:00:04,899 INFO    MainThread:1027956 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-08-05 11:00:04,899 INFO    MainThread:1027956 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /home/<USER>/GESCO/Image_JSCC/wandb/run-20250805_110004-n27dbfhz/logs/debug.log
2025-08-05 11:00:04,899 INFO    MainThread:1027956 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /home/<USER>/GESCO/Image_JSCC/wandb/run-20250805_110004-n27dbfhz/logs/debug-internal.log
2025-08-05 11:00:04,899 INFO    MainThread:1027956 [wandb_init.py:init():830] calling init triggers
2025-08-05 11:00:04,899 INFO    MainThread:1027956 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'image_height': 256, 'image_width': 512, 'batch_size': 4, 'epochs': 300, 'learning_rate': 0.0001, 'compression_ratio': 1, 'quantization_bits': 8, 'snr_dB': 10, 'channel_type': 'AWGN', '_wandb': {}}
2025-08-05 11:00:04,899 INFO    MainThread:1027956 [wandb_init.py:init():871] starting backend
2025-08-05 11:00:05,104 INFO    MainThread:1027956 [wandb_init.py:init():874] sending inform_init request
2025-08-05 11:00:05,105 INFO    MainThread:1027956 [wandb_init.py:init():882] backend started and connected
2025-08-05 11:00:05,106 INFO    MainThread:1027956 [wandb_init.py:init():953] updated telemetry
2025-08-05 11:00:05,112 INFO    MainThread:1027956 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-08-05 11:00:06,460 INFO    MainThread:1027956 [wandb_init.py:init():1029] starting run threads in backend
2025-08-05 11:00:06,538 INFO    MainThread:1027956 [wandb_run.py:_console_start():2458] atexit reg
2025-08-05 11:00:06,538 INFO    MainThread:1027956 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-08-05 11:00:06,539 INFO    MainThread:1027956 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-08-05 11:00:06,539 INFO    MainThread:1027956 [wandb_run.py:_redirect():2398] Redirects installed.
2025-08-05 11:00:06,540 INFO    MainThread:1027956 [wandb_init.py:init():1075] run started, returning control to user process
