2025-08-05 10:57:22,009 INFO    MainThread:1024995 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-08-05 10:57:22,010 INFO    MainThread:1024995 [wandb_setup.py:_flush():80] Configure stats pid to 1024995
2025-08-05 10:57:22,010 INFO    MainThread:1024995 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-08-05 10:57:22,010 INFO    MainThread:1024995 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/GESCO/Image_JSCC/wandb/settings
2025-08-05 10:57:22,010 INFO    MainThread:1024995 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-08-05 10:57:22,010 INFO    MainThread:1024995 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /home/<USER>/GESCO/Image_JSCC/wandb/run-20250805_105722-62ukp8rf/logs/debug.log
2025-08-05 10:57:22,010 INFO    MainThread:1024995 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /home/<USER>/GESCO/Image_JSCC/wandb/run-20250805_105722-62ukp8rf/logs/debug-internal.log
2025-08-05 10:57:22,010 INFO    MainThread:1024995 [wandb_init.py:init():830] calling init triggers
2025-08-05 10:57:22,010 INFO    MainThread:1024995 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'image_height': 512, 'image_width': 1024, 'batch_size': 4, 'epochs': 300, 'learning_rate': 0.0001, 'compression_ratio': 1, 'quantization_bits': 8, 'snr_dB': 10, 'channel_type': 'AWGN', '_wandb': {}}
2025-08-05 10:57:22,010 INFO    MainThread:1024995 [wandb_init.py:init():871] starting backend
2025-08-05 10:57:22,216 INFO    MainThread:1024995 [wandb_init.py:init():874] sending inform_init request
2025-08-05 10:57:22,218 INFO    MainThread:1024995 [wandb_init.py:init():882] backend started and connected
2025-08-05 10:57:22,219 INFO    MainThread:1024995 [wandb_init.py:init():953] updated telemetry
2025-08-05 10:57:22,226 INFO    MainThread:1024995 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
