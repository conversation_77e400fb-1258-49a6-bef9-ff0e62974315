2025-08-05 10:54:58,375 INFO    MainThread:1021735 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-08-05 10:54:58,375 INFO    MainThread:1021735 [wandb_setup.py:_flush():80] Configure stats pid to 1021735
2025-08-05 10:54:58,375 INFO    MainThread:1021735 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-08-05 10:54:58,375 INFO    MainThread:1021735 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/GESCO/Image_JSCC/wandb/settings
2025-08-05 10:54:58,375 INFO    MainThread:1021735 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-08-05 10:54:58,375 INFO    MainThread:1021735 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /home/<USER>/GESCO/Image_JSCC/wandb/run-20250805_105458-0eyvxvyr/logs/debug.log
2025-08-05 10:54:58,375 INFO    MainThread:1021735 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /home/<USER>/GESCO/Image_JSCC/wandb/run-20250805_105458-0eyvxvyr/logs/debug-internal.log
2025-08-05 10:54:58,375 INFO    MainThread:1021735 [wandb_init.py:init():830] calling init triggers
2025-08-05 10:54:58,376 INFO    MainThread:1021735 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'image_height': 1024, 'image_width': 2048, 'batch_size': 2, 'epochs': 300, 'learning_rate': 0.0001, 'compression_ratio': 1, 'quantization_bits': 8, 'snr_dB': 10, 'channel_type': 'AWGN', '_wandb': {}}
2025-08-05 10:54:58,376 INFO    MainThread:1021735 [wandb_init.py:init():871] starting backend
2025-08-05 10:54:58,580 INFO    MainThread:1021735 [wandb_init.py:init():874] sending inform_init request
2025-08-05 10:54:58,583 INFO    MainThread:1021735 [wandb_init.py:init():882] backend started and connected
2025-08-05 10:54:58,584 INFO    MainThread:1021735 [wandb_init.py:init():953] updated telemetry
2025-08-05 10:54:58,589 INFO    MainThread:1021735 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-08-05 10:54:59,496 INFO    MainThread:1021735 [wandb_init.py:init():1029] starting run threads in backend
2025-08-05 10:54:59,576 INFO    MainThread:1021735 [wandb_run.py:_console_start():2458] atexit reg
2025-08-05 10:54:59,576 INFO    MainThread:1021735 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-08-05 10:54:59,576 INFO    MainThread:1021735 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-08-05 10:54:59,576 INFO    MainThread:1021735 [wandb_run.py:_redirect():2398] Redirects installed.
2025-08-05 10:54:59,578 INFO    MainThread:1021735 [wandb_init.py:init():1075] run started, returning control to user process
