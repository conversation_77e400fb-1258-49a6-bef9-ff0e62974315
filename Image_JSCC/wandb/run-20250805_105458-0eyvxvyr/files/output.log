Configuration loaded:
  Image size: 1024x2048x3
  Batch size: 2
  Epochs: 300
  Compression ratio: 1
  SNR: 10 dB
  Channel type: AWGN

Loading datasets...
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Found 500 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/val
Steps per epoch: 1487

Creating model...
/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/layers/layer.py:421: UserWarning: `build()` was called on layer 'image_encoder', however the layer does not have a `build()` method implemented and it looks like it has unbuilt state. This will cause the layer to be marked as built, despite not being actually built, which may cause failures down the line. Make sure to implement a proper `build()` method.
  warnings.warn(
Traceback (most recent call last):
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 290, in <module>
    main()
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 159, in main
    model = create_model(config)
  File "/home/<USER>/GESCO/Image_JSCC/model/image_jscc_model.py", line 157, in create_model
    _ = model(dummy_input, training=False)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 122, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "/home/<USER>/GESCO/Image_JSCC/model/image_jscc_model.py", line 71, in call
    encoded_features = self.encoder(inputs, training=training)
  File "/home/<USER>/GESCO/Image_JSCC/layers/image_encoder.py", line 140, in call
    attention_output = layer['attention'](x, training=training)
  File "/home/<USER>/GESCO/Image_JSCC/layers/attention_modules.py", line 211, in call
    y = scaled_dot_product_attention(inputs=[
  File "/home/<USER>/GESCO/Image_JSCC/layers/attention_modules.py", line 25, in call
    e = K.batch_dot(query, key, axes=2) / K.sqrt(K.cast(feature_dim, dtype=K.floatx()))
tensorflow.python.framework.errors_impl.ResourceExhaustedError: Exception encountered when calling ScaledDotProductAttention.call().

[1m{{function_node __wrapped__BatchMatMulV2_device_/job:localhost/replica:0/task:0/device:GPU:0}} OOM when allocating tensor with shape[8,32768,32768] and type float on /job:localhost/replica:0/task:0/device:GPU:0 by allocator GPU_0_bfc [Op:BatchMatMulV2] name: [0m

Arguments received by ScaledDotProductAttention.call():
  • inputs=['tf.Tensor(shape=(8, 32768, 32), dtype=float32)', 'tf.Tensor(shape=(8, 32768, 32), dtype=float32)', 'tf.Tensor(shape=(8, 32768, 32), dtype=float32)']
  • mask=['None', 'None', 'None']
  • kwargs=<class 'inspect._empty'>
Traceback (most recent call last):
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 290, in <module>
    main()
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 159, in main
    model = create_model(config)
  File "/home/<USER>/GESCO/Image_JSCC/model/image_jscc_model.py", line 157, in create_model
    _ = model(dummy_input, training=False)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 122, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "/home/<USER>/GESCO/Image_JSCC/model/image_jscc_model.py", line 71, in call
    encoded_features = self.encoder(inputs, training=training)
  File "/home/<USER>/GESCO/Image_JSCC/layers/image_encoder.py", line 140, in call
    attention_output = layer['attention'](x, training=training)
  File "/home/<USER>/GESCO/Image_JSCC/layers/attention_modules.py", line 211, in call
    y = scaled_dot_product_attention(inputs=[
  File "/home/<USER>/GESCO/Image_JSCC/layers/attention_modules.py", line 25, in call
    e = K.batch_dot(query, key, axes=2) / K.sqrt(K.cast(feature_dim, dtype=K.floatx()))
tensorflow.python.framework.errors_impl.ResourceExhaustedError: Exception encountered when calling ScaledDotProductAttention.call().

[1m{{function_node __wrapped__BatchMatMulV2_device_/job:localhost/replica:0/task:0/device:GPU:0}} OOM when allocating tensor with shape[8,32768,32768] and type float on /job:localhost/replica:0/task:0/device:GPU:0 by allocator GPU_0_bfc [Op:BatchMatMulV2] name: [0m

Arguments received by ScaledDotProductAttention.call():
  • inputs=['tf.Tensor(shape=(8, 32768, 32), dtype=float32)', 'tf.Tensor(shape=(8, 32768, 32), dtype=float32)', 'tf.Tensor(shape=(8, 32768, 32), dtype=float32)']
  • mask=['None', 'None', 'None']
  • kwargs=<class 'inspect._empty'>
