2025-08-05 10:50:47,200 INFO    MainThread:1017744 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-08-05 10:50:47,200 INFO    MainThread:1017744 [wandb_setup.py:_flush():80] Configure stats pid to 1017744
2025-08-05 10:50:47,200 INFO    MainThread:1017744 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-08-05 10:50:47,200 INFO    MainThread:1017744 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/GESCO/Image_JSCC/wandb/settings
2025-08-05 10:50:47,200 INFO    MainThread:1017744 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-08-05 10:50:47,200 INFO    MainThread:1017744 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /home/<USER>/GESCO/Image_JSCC/wandb/run-20250805_105047-f9ej5y1y/logs/debug.log
2025-08-05 10:50:47,200 INFO    MainThread:1017744 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /home/<USER>/GESCO/Image_JSCC/wandb/run-20250805_105047-f9ej5y1y/logs/debug-internal.log
2025-08-05 10:50:47,200 INFO    MainThread:1017744 [wandb_init.py:init():830] calling init triggers
2025-08-05 10:50:47,201 INFO    MainThread:1017744 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'image_height': 512, 'image_width': 1024, 'batch_size': 4, 'epochs': 100, 'learning_rate': 5e-05, 'compression_ratio': 1, 'quantization_bits': 8, 'snr_dB': 10, 'channel_type': 'AWGN', '_wandb': {}}
2025-08-05 10:50:47,201 INFO    MainThread:1017744 [wandb_init.py:init():871] starting backend
2025-08-05 10:50:47,407 INFO    MainThread:1017744 [wandb_init.py:init():874] sending inform_init request
2025-08-05 10:50:47,410 INFO    MainThread:1017744 [wandb_init.py:init():882] backend started and connected
2025-08-05 10:50:47,411 INFO    MainThread:1017744 [wandb_init.py:init():953] updated telemetry
2025-08-05 10:50:47,417 INFO    MainThread:1017744 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-08-05 10:50:48,768 INFO    MainThread:1017744 [wandb_init.py:init():1029] starting run threads in backend
2025-08-05 10:50:48,863 INFO    MainThread:1017744 [wandb_run.py:_console_start():2458] atexit reg
2025-08-05 10:50:48,863 INFO    MainThread:1017744 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-08-05 10:50:48,863 INFO    MainThread:1017744 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-08-05 10:50:48,863 INFO    MainThread:1017744 [wandb_run.py:_redirect():2398] Redirects installed.
2025-08-05 10:50:48,865 INFO    MainThread:1017744 [wandb_init.py:init():1075] run started, returning control to user process
